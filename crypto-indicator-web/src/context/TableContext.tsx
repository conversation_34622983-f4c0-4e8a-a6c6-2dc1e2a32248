import React, { createContext, useReducer, useMemo, type ReactNode, type Dispatch } from 'react';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';
import type {
  FilterConfig,
  SortColumn,
  SortDirection,
  StockFilterConfig,
  StockSortColumn,
} from '@/types/table';

// Table State Interface
interface TableState {
  // Data
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  loading: boolean;
  error: string | null;
  
  // Asset type
  assetType: 'crypto' | 'stock';
  
  // Filtering
  filterConfig: FilterConfig | StockFilterConfig;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  
  // Sorting
  sortConfig: {
    column: SortColumn | StockSortColumn | null;
    direction: SortDirection;
  };
}

// Table Actions
type TableAction = 
  | { type: 'SET_DATA'; payload: { data: any[], btcData: any[], totalCount: number } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_FILTER'; payload: Partial<FilterConfig | StockFilterConfig> }
  | { type: 'SET_SORT'; payload: { column: SortColumn | StockSortColumn; direction: SortDirection } }
  | { type: 'SET_ASSET_TYPE'; payload: 'crypto' | 'stock' }
  | { type: 'SET_FILTERED_COUNT'; payload: number }
  | { type: 'SET_ACTIVE_FILTERS'; payload: boolean };

// Table Actions Interface
interface TableActions {
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  onSort: (column: SortColumn | StockSortColumn) => void;
  getSortDirection: (column: SortColumn | StockSortColumn) => SortDirection;
  onFilterChange: (filters: Partial<FilterConfig | StockFilterConfig>) => void;
  onClearFilters: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

// Context Value Interface
interface TableContextValue {
  state: TableState;
  dispatch: Dispatch<TableAction>;
  actions: TableActions;
}

// Default filter configurations
const DEFAULT_CRYPTO_FILTERS: FilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

const DEFAULT_STOCK_FILTERS: StockFilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
};

// Initial state factory
const createInitialState = (assetType: 'crypto' | 'stock'): TableState => ({
  data: [],
  btcStatistics: [],
  loading: false,
  error: null,
  assetType,
  filterConfig: assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS,
  hasActiveFilters: false,
  filteredCount: 0,
  totalCount: 0,
  sortConfig: {
    column: null,
    direction: null,
  },
});

// Table Reducer
const tableReducer = (state: TableState, action: TableAction): TableState => {
  switch (action.type) {
    case 'SET_DATA':
      return {
        ...state,
        data: action.payload.data,
        btcStatistics: action.payload.btcData,
        totalCount: action.payload.totalCount,
        filteredCount: action.payload.data.length,
        loading: false,
        error: null,
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload,
        ...(action.payload && { error: null }),
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    
    case 'SET_FILTER':
      return {
        ...state,
        filterConfig: { ...state.filterConfig, ...action.payload },
      };
    
    case 'SET_SORT':
      return {
        ...state,
        sortConfig: {
          column: action.payload.column,
          direction: action.payload.direction,
        },
      };
    
    case 'SET_ASSET_TYPE':
      return {
        ...state,
        assetType: action.payload,
        filterConfig: action.payload === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS,
        sortConfig: { column: null, direction: null },
      };
    
    case 'SET_FILTERED_COUNT':
      return {
        ...state,
        filteredCount: action.payload,
      };
    
    case 'SET_ACTIVE_FILTERS':
      return {
        ...state,
        hasActiveFilters: action.payload,
      };
    
    default:
      return state;
  }
};

// Create Context
const TableContext = createContext<TableContextValue | null>(null);

// Table Provider Props
interface TableProviderProps {
  children: ReactNode;
  assetType: 'crypto' | 'stock';
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

// Table Provider Component
export const TableProvider: React.FC<TableProviderProps> = ({
  children,
  assetType,
  onSignalClick,
  onRefresh,
  formatDate,
  findBtcDataForSymbol,
}) => {
  const [state, dispatch] = useReducer(tableReducer, createInitialState(assetType));

  // Memoized actions to prevent unnecessary re-renders
  const actions = useMemo<TableActions>(() => ({
    onSignalClick,
    onRefresh,
    
    onSort: (column: SortColumn | StockSortColumn) => {
      const currentDirection = state.sortConfig.column === column ? state.sortConfig.direction : null;
      let newDirection: SortDirection;
      
      if (currentDirection === null) {
        newDirection = 'desc';
      } else if (currentDirection === 'desc') {
        newDirection = 'asc';
      } else {
        newDirection = null;
      }
      
      dispatch({
        type: 'SET_SORT',
        payload: { column: newDirection ? column : null, direction: newDirection },
      });
    },
    
    getSortDirection: (column: SortColumn | StockSortColumn) => {
      return state.sortConfig.column === column ? state.sortConfig.direction : null;
    },
    
    onFilterChange: (filters: Partial<FilterConfig | StockFilterConfig>) => {
      dispatch({ type: 'SET_FILTER', payload: filters });
    },
    
    onClearFilters: () => {
      const defaultFilters = assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      dispatch({ type: 'SET_FILTER', payload: defaultFilters });
    },
    
    formatDate,
    findBtcDataForSymbol,
  }), [
    onSignalClick,
    onRefresh,
    formatDate,
    findBtcDataForSymbol,
    state.sortConfig,
    assetType,
  ]);

  const contextValue = useMemo<TableContextValue>(() => ({
    state,
    dispatch,
    actions,
  }), [state, actions]);

  return (
    <TableContext.Provider value={contextValue}>
      {children}
    </TableContext.Provider>
  );
};

export { TableContext };
export type { TableState, TableAction, TableActions, TableContextValue };
