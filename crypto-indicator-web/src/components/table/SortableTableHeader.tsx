import React from 'react';

import { STOCK_TABLE_HEADERS, TABLE_HEADERS } from '@/constants/app';
import { useTableSorting, useTableState } from '@/hooks/useTableContext';

import type { SortColumn, SortDirection, StockSortColumn } from '@/types/table';

const getSortIcon = (direction: SortDirection): string => {
  switch (direction) {
    case 'asc': {
      return '▲';
    }
    case 'desc': {
      return '▼';
    }
    default: {
      return '↕';
    }
  }
};

const getAriaSort = (direction: SortDirection | null): 'ascending' | 'descending' | 'none' => {
  if (direction === 'asc') {return 'ascending';}
  if (direction === 'desc') {return 'descending';}
  return 'none';
};

const CRYPTO_COLUMN_MAPPING: Record<string, SortColumn> = {
  [TABLE_HEADERS.CRYPTOCURRENCY]: 'symbol',
  [TABLE_HEADERS.USD_PRICE]: 'usdPrice',
  [TABLE_HEADERS.MARKET_CAP]: 'marketCap',
  [TABLE_HEADERS.USD_SIGNAL]: 'usdSignal',
  [TABLE_HEADERS.BTC_PRICE]: 'btcPrice',
  [TABLE_HEADERS.BTC_SIGNAL]: 'btcSignal',
};

const STOCK_COLUMN_MAPPING: Record<string, StockSortColumn> = {
  [STOCK_TABLE_HEADERS.STOCK]: 'symbol',
  [STOCK_TABLE_HEADERS.PRICE]: 'usdPrice',
  [STOCK_TABLE_HEADERS.VOLUME]: 'marketCap',
  [STOCK_TABLE_HEADERS.SIGNAL]: 'usdSignal',
  [STOCK_TABLE_HEADERS.BTC_PRICE]: 'btcPrice',
  [STOCK_TABLE_HEADERS.BTC_SIGNAL]: 'btcSignal',
};

const renderCryptoHeaders = (renderSortableHeader: (headerText: string) => JSX.Element) => (
  <>
    {renderSortableHeader(TABLE_HEADERS.CRYPTOCURRENCY)}
    {renderSortableHeader(TABLE_HEADERS.USD_PRICE)}
    {renderSortableHeader(TABLE_HEADERS.MARKET_CAP)}
    {renderSortableHeader(TABLE_HEADERS.USD_SIGNAL)}
    {renderSortableHeader(TABLE_HEADERS.BTC_PRICE)}
    {renderSortableHeader(TABLE_HEADERS.BTC_SIGNAL)}
  </>
);

const renderStockHeaders = (renderSortableHeader: (headerText: string) => JSX.Element) => (
  <>
    {renderSortableHeader(STOCK_TABLE_HEADERS.STOCK)}
    {renderSortableHeader(STOCK_TABLE_HEADERS.PRICE)}
    {renderSortableHeader(STOCK_TABLE_HEADERS.VOLUME)}
    {renderSortableHeader(STOCK_TABLE_HEADERS.SIGNAL)}
    {renderSortableHeader(STOCK_TABLE_HEADERS.BTC_PRICE)}
    {renderSortableHeader(STOCK_TABLE_HEADERS.BTC_SIGNAL)}
  </>
);

export const SortableTableHeader: React.FC<SortableTableHeaderProps> = ({
  onSort,
  getSortDirection,
  assetType = 'crypto',
}) => {
  const columnMapping = assetType === 'crypto' ? CRYPTO_COLUMN_MAPPING : STOCK_COLUMN_MAPPING;

  const handleHeaderClick = (headerText: string) => {
    const column = columnMapping[headerText];
    if (column) {
      onSort(column);
    }
  };

  const renderSortableHeader = (headerText: string) => {
    const column = columnMapping[headerText];
    const direction = column ? getSortDirection(column) : null;
    const isActive = direction !== null;

    return (
      <th
        key={headerText}
        className={`sortable-header ${isActive ? 'active' : ''}`}
        onClick={() => { handleHeaderClick(headerText); }}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleHeaderClick(headerText);
          }
        }}
        aria-sort={getAriaSort(direction)}
      >
        <div className="header-content">
          <span className="header-text">{headerText}</span>
          <span className="sort-icon">{getSortIcon(direction)}</span>
        </div>
      </th>
    );
  };

  return (
    <thead>
      <tr>
        {assetType === 'crypto'
          ? renderCryptoHeaders(renderSortableHeader)
          : renderStockHeaders(renderSortableHeader)
        }
      </tr>
    </thead>
  );
};
