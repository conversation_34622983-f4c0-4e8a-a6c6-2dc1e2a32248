import React from 'react';

import { CSS_CLASSES } from '@/constants/app';
import {
  useTableAssetType,
  useTableData,
  useTableInteractions,
  useTableState} from '@/hooks/useTableContext';

import { CryptoTableRow } from './CryptoTableRow';
import { SortableTableHeader } from './SortableTableHeader';
import { TableFilters } from './TableFilters';

import type { CryptoCurrencyStatisticsDto } from '@/generated';

export const DesktopLayout: React.FC = () => {
  const { data, btcStatistics } = useTableData();
  const { assetType } = useTableState();
  const { findBtcDataForSymbol } = useTableAssetType();

  return (
    <div className="desktop-layout">
      <TableFilters />

      <div className="table-wrapper">
        <table className={CSS_CLASSES.TABLE}>
          <SortableTableHeader />
          <tbody>
            {data.map(item => {
              const btcData = findBtcDataForSymbol(btcStatistics, item.symbol);
              return (
                <CryptoTableRow
                  key={item.symbol}
                  crypto={item as CryptoCurrencyStatisticsDto}
                  {...(btcData && { btcData })}
                />
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};
