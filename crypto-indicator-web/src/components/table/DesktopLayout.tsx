import React from 'react';

import { CSS_CLASSES } from '@/constants/app';
import {
  useTableData,
  useTableState,
  useTableAssetType,
  useTableInteractions
} from '@/hooks/useTableContext';

import { CryptoTableRow } from './CryptoTableRow';
import { SortableTableHeader } from './SortableTableHeader';
import { TableFilters } from './TableFilters';

import type { CryptoCurrencyStatisticsDto } from '@/generated';

export const DesktopLayout: React.FC<DesktopLayoutProps> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  assetType = 'crypto',
}) => {
  return (
    <div className="desktop-layout">
      <TableFilters
        filterConfig={filterConfig}
        onSymbolSearchChange={onSymbolSearchChange}
        onUsdSignalChange={onUsdSignalChange}
        onBtcSignalChange={onBtcSignalChange}
        onClearFilters={onClearFilters}
        hasActiveFilters={hasActiveFilters}
        filteredCount={filteredCount}
        totalCount={totalCount}
        assetType={assetType}
      />

      <div className="table-wrapper">
        <table className={CSS_CLASSES.TABLE}>
          <SortableTableHeader
            onSort={onSort}
            getSortDirection={getSortDirection}
            assetType={assetType}
          />
          <tbody>
            {data.map(item => {
              const btcData = findBtcDataForSymbol(btcStatistics, item.symbol);
              return (
                <CryptoTableRow
                  key={item.symbol}
                  crypto={item as CryptoCurrencyStatisticsDto}
                  {...(btcData && { btcData })}
                  onSignalClick={onSignalClick}
                  formatDate={formatDate}
                  assetType={assetType}
                />
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};
