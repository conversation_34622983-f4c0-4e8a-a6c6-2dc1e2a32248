import React from 'react';

import { TableControls } from '@/components/ui/TableControls';
import { CSS_CLASSES } from '@/constants/app';
import { useTableState, useTableData, useTableAssetType } from '@/hooks/useTableContext';

import { ResponsiveTableContainer } from './ResponsiveTableContainer';

interface MainContentProps {
  showTableControls?: boolean;
  className?: string;
}

export const MainContent: React.FC<MainContentProps> = ({
  showTableControls = true,
  className
}) => {
  const { assetType } = useTableState();
  const { data, loading } = useTableData();

  // Determine container class based on asset type or custom className
  const containerClass = className ||
    (assetType === 'crypto' ? CSS_CLASSES.TABLE_CONTAINER : 'stock-main-content');

  return (
    <div className={containerClass}>
      {/* Show table controls only for crypto and when enabled */}
      {showTableControls && assetType === 'crypto' && <TableControls />}

      {/* Responsive table container - no props needed, uses context */}
      <ResponsiveTableContainer />

      {/* Stock-specific no data message */}
      {assetType === 'stock' && data.length === 0 && !loading && (
        <div className="no-data">
          <p>No stock data available.</p>
        </div>
      )}
    </div>
  );
};
