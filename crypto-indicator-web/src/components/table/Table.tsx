import React from 'react';

import { TableProvider } from '@/context/TableContext';

import { Card } from '../card/Card';
import { FilterDrawer } from '../filters/FilterDrawer';
import { FilterToggle } from '../filters/FilterToggle';
import { TableControls } from '../ui/TableControls';

import { CryptoTableRow } from './CryptoTableRow';
import { DesktopLayout } from './DesktopLayout';
import { MainContent } from './MainContent';
import { MobileLayout } from './MobileLayout';
import { ResponsiveTableContainer } from './ResponsiveTableContainer';
import { SortableTableHeader } from './SortableTableHeader';
import { TabletLayout } from './TabletLayout';
import { TableFilters } from './TableFilters';

import type { ReactNode } from 'react';
import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';

// Table Provider Props
interface TableProviderProps {
  children: ReactNode;
  assetType: 'crypto' | 'stock';
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Table Compound Component System
 * 
 * This provides a clean, composable API for table functionality:
 * 
 * @example
 * ```tsx
 * <Table.Provider assetType="crypto" onSignalClick={handleSignalClick} onRefresh={handleRefresh}>
 *   <Table.Content />
 * </Table.Provider>
 * 
 * <Table.Provider assetType="stock" onSignalClick={handleSignalClick} onRefresh={handleRefresh}>
 *   <Table.Content showTableControls={false} />
 * </Table.Provider>
 * ```
 */
export const Table = {
  // Core Provider
  Provider: ({ children, ...props }: TableProviderProps) => (
    <TableProvider {...props}>
      {children}
    </TableProvider>
  ),

  // Main Components
  Content: MainContent,
  Container: ResponsiveTableContainer,
  Controls: TableControls,
  Filters: TableFilters,

  // Layout Components
  DesktopLayout,
  TabletLayout,
  MobileLayout,

  // Table Components
  Header: SortableTableHeader,
  Row: CryptoTableRow,

  // Card Components
  Card,

  // Filter Components
  FilterToggle,
  FilterDrawer,
} as const;

// Export individual components for direct use if needed
export {
  MainContent,
  ResponsiveTableContainer,
  TableControls,
  TableFilters,
  DesktopLayout,
  TabletLayout,
  MobileLayout,
  SortableTableHeader,
  CryptoTableRow,
  Card,
  FilterToggle,
  FilterDrawer,
};

// Export the provider separately for flexibility
export { TableProvider };
