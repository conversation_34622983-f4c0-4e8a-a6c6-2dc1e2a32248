import React from 'react';

import { useTableData,useTableFiltering } from '@/hooks/useTableContext';

interface FilterToggleProps {
  onClick: () => void;
}

export const FilterToggle: React.FC<FilterToggleProps> = ({
  onClick,
}) => {
  const { hasActiveFilters } = useTableFiltering();
  const { filteredCount } = useTableData();
  return (
    <div className="filter-toggle-container">
      <button
        className={`filter-toggle ${hasActiveFilters ? 'active' : ''}`}
        onClick={onClick}
        aria-label="Open filters"
      >
        <span className="filter-icon">⚙️</span>
        {hasActiveFilters && (
          <span className="filter-badge">
            {filteredCount}
          </span>
        )}
      </button>
      
      {hasActiveFilters && (
        <div className="filter-status">
          {filteredCount} of {totalCount}
        </div>
      )}
    </div>
  );
};
