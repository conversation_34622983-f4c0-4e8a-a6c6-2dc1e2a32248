import { filterByCurrency } from '@/utils/dataProcessors';

import type { StockIndicatorValueDto, StockStatisticsDto } from '@/generated';

/**
 * Process stock statistics data for table display
 */
export const processStockStatistics = (statistics: StockStatisticsDto[]) => {
  const stockStatistics = statistics.filter(
    (stat): stat is StockStatisticsDto => stat.indicatorValues.length > 0,
  );

  // find btc data for each stock
  const btcStatistics = filterByCurrency(stockStatistics, 'BTC');

  return {
    stockStatistics: stockStatistics,
    btcStatistics: btcStatistics as StockStatisticsDto[],
    totalCount: stockStatistics.length,
  };
};

/**
 * Get the latest indicator value for a stock
 */
export const getLatestStockData = (
  stock: StockStatisticsDto,
): StockIndicatorValueDto | undefined => {
  if (stock.indicatorValues.length === 0) {
    return undefined;
  }

  // Return the most recent indicator value (last in array)
  return stock.indicatorValues.at(-1);
};
