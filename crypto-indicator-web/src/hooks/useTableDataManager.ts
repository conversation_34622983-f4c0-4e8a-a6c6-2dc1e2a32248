import { useCallback, useEffect, useMemo, useState } from 'react';

import { useChartData } from '@/hooks/useChartData';
import { useCryptoData } from '@/hooks/useCryptoData';
import { useFiltering } from '@/hooks/useFiltering';
import { useSorting } from '@/hooks/useSorting';
import { useStockChartData } from '@/hooks/useStockChartData';
import { useStockData } from '@/hooks/useStockData';
import { useStockFiltering } from '@/hooks/useStockFiltering';
import { useStockSorting } from '@/hooks/useStockSorting';
import {
  findBtcDataForSymbol,
  formatDate,
  processCryptoStatistics,
} from '@/utils/dataProcessors';
import { processStockStatistics } from '@/utils/stockDataProcessors';
import { applyStockFilters } from '@/utils/stockTableFiltering';
import { applyStockSorting } from '@/utils/stockTableSorting';
import { applyFilters } from '@/utils/tableFiltering';
import { applySorting } from '@/utils/tableSorting';

import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from '@/generated';

interface UseTableDataManagerProps {
  assetType: 'crypto' | 'stock';
}

interface TableDataManagerReturn {
  // Processed data
  processedData: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  error: string | null;

  // Chart data
  chartData: unknown;
  showChart: boolean;
  setShowChart: (show: boolean) => void;

  // Actions
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;

  // Utilities
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Unified hook that manages all table data logic for both crypto and stock
 * This replaces the massive StatisticsTable component logic
 */
// eslint-disable-next-line max-lines-per-function
export const useTableDataManager = ({
  assetType
}: UseTableDataManagerProps): TableDataManagerReturn => {
  const [showChart, setShowChart] = useState(false);

  // Crypto hooks
  const {
    data: cryptoStatistics,
    loading: cryptoLoading,
    error: cryptoError,
    fetchData: fetchCryptoData,
  } = useCryptoData();

  const {
    chartData: cryptoChartData,
    setChartData: setCryptoChartData,
  } = useChartData();

  const {
    filterConfig: cryptoFilterConfig,
  } = useFiltering();

  const {
    sortConfig: cryptoSortConfig,
  } = useSorting();

  // Stock hooks
  const {
    data: stockStatistics,
    loading: stockLoading,
    error: stockError,
    fetchData: fetchStockData,
  } = useStockData();

  const {
    chartData: stockChartData,
    setChartData: setStockChartData,
  } = useStockChartData();

  const {
    filterConfig: stockFilterConfig,
  } = useStockFiltering();

  const {
    sortConfig: stockSortConfig,
  } = useStockSorting();

  // Select data based on asset type
  const rawData = assetType === 'crypto' ? cryptoStatistics : stockStatistics;
  const loading = assetType === 'crypto' ? cryptoLoading : stockLoading;
  const error = assetType === 'crypto' ? cryptoError : stockError;
  const chartData = assetType === 'crypto' ? cryptoChartData : stockChartData;

  // Process and filter data
  const processedData = useMemo(() => {
    if (!rawData?.length) {return [];}
    
    return assetType === 'crypto' 
      ? processCryptoStatistics(rawData as CryptoCurrencyStatisticsDto[])
      : processStockStatistics(rawData as StockStatisticsDto[]);
  }, [rawData, assetType]);

  const filteredData = useMemo(() => {
    if (!processedData?.length) {return [];}
    
    if (assetType === 'crypto') {
      const filtered = applyFilters(
        processedData as CryptoCurrencyStatisticsDto[], 
        cryptoFilterConfig, 
        findBtcDataForSymbol
      );
      return applySorting(filtered, cryptoSortConfig);
    } 
      const filtered = applyStockFilters(
        processedData as StockStatisticsDto[], 
        stockFilterConfig
      );
      return applyStockSorting(filtered, stockSortConfig);
    
  }, [
    processedData, 
    assetType, 
    cryptoFilterConfig, 
    cryptoSortConfig, 
    stockFilterConfig, 
    stockSortConfig
  ]);

  // BTC statistics (only for crypto)
  const btcStatistics = useMemo(() => {
    return assetType === 'crypto' ? processedData : [];
  }, [processedData, assetType]);

  // Signal click handler
  const onSignalClick = useCallback(async (symbol: string, currency: string) => {
    try {
      if (assetType === 'crypto') {
        setCryptoChartData({ symbol, currency });
      } else {
        setStockChartData({ symbol, currency });
      }
      setShowChart(true);
    } catch (error) {
      console.error('Failed to handle signal click:', error);
    }
  }, [assetType, setCryptoChartData, setStockChartData]);

  // Refresh handler
  const onRefresh = useCallback(() => {
    if (assetType === 'crypto') {
      fetchCryptoData();
    } else {
      fetchStockData();
    }
  }, [assetType, fetchCryptoData, fetchStockData]);

  // Auto-refresh effect
  useEffect(() => {
    const interval = setInterval(() => {
      onRefresh();
    }, 30_000); // 30 seconds

    return () => { clearInterval(interval); };
  }, [onRefresh]);

  return {
    // Processed data
    processedData: filteredData,
    btcStatistics: btcStatistics as CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    totalCount: processedData.length,
    filteredCount: filteredData.length,
    loading,
    error,
    
    // Chart data
    chartData,
    showChart,
    setShowChart,
    
    // Actions
    onSignalClick,
    onRefresh,
    
    // Utilities
    formatDate,
    findBtcDataForSymbol,
  };
};
